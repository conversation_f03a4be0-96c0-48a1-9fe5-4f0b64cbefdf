var sceneReadyBehavior = require('../behavior-scene/scene-ready');
var yuvBufferToImage = require('./utils/yuvBufferToImage')
var FormData = require('../../utils/form-data/formData')
var wasmLoader = require('../../utils/wasmLoader')
var markerTransform = require('./utils/markerTransform')
var vpsService = require('../../services/vpsService');
const { AnchorData, AnchorPose } = require('../../data/models');
const { roomsData } = require('./configs/rooms-data');
let trackingMarker = null
let lastVpsRoamingSuccess = false
let vpsRetryTimeoutId = null
const distanceThreshold = 1.8

const xr = wx.getXrFrameSystem();

Page({
  behaviors:[sceneReadyBehavior],
  data: {
    anchorList: [],
    initialVpsTracked: false,
    showBackBtn: true,
    isProximityCheckOn: false,
    isVisitingItem: false,
    markerLeft: 50,
    markerTop: 50,
    markerWidth: 0,
    markerHeight: 0,
    unfinishedItemsCount: 0,
    checkDistanceIntervalId: null,
    transformMatrix: null,
    arTracked: false,
    // debug only
    cameraPoseText: '',
    originalPoseText: ''
  },
  arRawData: null,
  userPose: null,

  origUserPose: null,
  markerLockComponent: null,
  currentRoom: null,
  targetRoom: null,
  onLoad() {
    if (!this.data.arTracked) {
      wx.showLoading({
        title: 'AR初始化',
      })
    }
    this.currentRoom = roomsData[0]
  },
  onUnload() {
    if (vpsRetryTimeoutId) {
      clearTimeout(vpsRetryTimeoutId)
      vpsRetryTimeoutId = null
    }
    if (this.data.isProximityCheckOn) {
      this.stopTracking()
    }
  },
  calculateTransformationMatrices(newMarkerPose) {
    // 从本地缓存加载建图时存储的marker在AR坐标系下的位姿
    const markerRecordedSlamPose = wx.getStorageSync(newMarkerPose.id);
    console.log("初始SLAM pose为: position: " + JSON.stringify(markerRecordedSlamPose.position) + ", rotation: " + JSON.stringify(markerRecordedSlamPose.rotation))
    console.log("新SLAM pose为：position: " + JSON.stringify(newMarkerPose.position) + ", rotation: " + JSON.stringify(newMarkerPose.rotation))
    const {R, T} = markerTransform.calculateTransformationMatrix(markerRecordedSlamPose, newMarkerPose)
    this.setData({
      rotationTransform: R,
      translationTransform: T
    })
  },
  // 申请初始定位
  async requestVpsEvent(evt) {
    if (!this.data.arTracked) {
      return
    }
    const { projectId } = evt.currentTarget.dataset;
    console.log('vps projectId: '+projectId)
    // console.log('----请求定位时，相机的原始位姿是-----: x: '+this.origUserPose.position.x+', y: '+this.origUserPose.position.y+', z: '+queryPose.position.z+', rotation: x: '+euler.x+', y: '+euler.y+', z: '+euler.z)
    //此处使用AR系统纠偏后的相机位姿，是因为该位姿已经经历1.与相机原点纠偏操作，2并对齐UniCity的右手系位姿一致
    let queryPose = {
      position: xr.Vector3.createFromNumber(this.userPose.position.x, this.userPose.position.y, this.userPose.position.z),
      quaternion: this.userPose.quaternion
    }

    await this.saveCurrentFrame(projectId, queryPose)
  },
  async requestVps(projectId) {
    if (!this.data.arTracked) {
      return
    }
    // console.log('----请求定位时，相机的原始位姿是-----: x: '+this.origUserPose.position.x+', y: '+this.origUserPose.position.y+', z: '+queryPose.position.z+', rotation: x: '+euler.x+', y: '+euler.y+', z: '+euler.z)
    //此处使用AR系统纠偏后的相机位姿，是因为该位姿已经经历1.与相机原点纠偏操作，2并对齐UniCity的右手系位姿一致
    let queryPose = {
      position: xr.Vector3.createFromNumber(this.userPose.position.x, this.userPose.position.y, this.userPose.position.z),
      quaternion: this.userPose.quaternion
    }

    await this.saveCurrentFrame(projectId, queryPose)
  },
  async saveCurrentFrame(projectId, queryPose) {
    try {
      if (!this.arRawData) {
        console.log('arRawData is empty')
        return
      }
      wx.showLoading({
        title: '定位中',
      })
      const {yBuffer, uvBuffer, width, height, intrinsics} = this.arRawData
      const imageBuffer = await yuvBufferToImage.yuvToImage(yBuffer, uvBuffer, width, height)
      const vpsIntrinsics = [intrinsics[0],intrinsics[4],intrinsics[6],intrinsics[7],width,height]
      console.log('intrinsics raw: '+intrinsics)
      console.log('intrinsics: '+vpsIntrinsics)
      const formData = new FormData()
      formData.appendFile('file', imageBuffer, 'pic.jpg')
      formData.append('latitude', '39.992194')
      formData.append('longitude', '116.329943')
      formData.append('type', 2)
      formData.append('projectId', projectId)
      formData.append('userId', '6')

      formData.append('intrinsics', vpsIntrinsics)

      const euler = queryPose.quaternion.toEulerAngles()
      console.log('queryPose: position: x: '+queryPose.position.x+', y: '+queryPose.position.y+', z: '+queryPose.position.z+', rotation: x: '+euler.x+', y: '+euler.y+', z: '+euler.z)

      const vpsInfo = await vpsService.vpsRoam(formData)
      const vpsPosition = {x: vpsInfo.tcw[0], y: vpsInfo.tcw[1], z: vpsInfo.tcw[2]}
      const vpsQuaternion = xr.Quaternion.createFromNumber(vpsInfo.qcw[1], vpsInfo.qcw[2], vpsInfo.qcw[3], vpsInfo.qcw[0])
      const vpsEuler = vpsQuaternion.toEulerAngles()
      const vpsPose = {
        position: xr.Vector3.createFromNumber(vpsPosition.x, vpsPosition.y, vpsPosition.z),
        quaternion: vpsQuaternion
      }
      const cameraEuler = queryPose.quaternion.toEulerAngles()
      console.log('--------queryPose的值是------: position: x: '+queryPose.position.x+', y: '+queryPose.position.y+', z: '+queryPose.position.z+', rotation: x: '+markerTransform.radianToAngle(cameraEuler.x)+', y: '+markerTransform.radianToAngle(cameraEuler.y)+', z: '+markerTransform.radianToAngle(cameraEuler.z))

      console.log(" --------vpsPose的值是------:"+JSON.stringify(vpsPose.position) +","+"vpsPose rotation:"+ JSON.stringify(vpsPose.quaternion.toEulerAngles()))

      const transformMatrix = markerTransform.calculateTransformationMatrix(queryPose, vpsPose)
      this.setData({
        transformMatrix: transformMatrix
      })
      lastVpsRoamingSuccess = true
      if (vpsRetryTimeoutId) {
        clearTimeout(vpsRetryTimeoutId)
        vpsRetryTimeoutId = null
      }
      wx.hideLoading()
      wx.showToast({
        title: '定位成功',
        icon: 'success'
      })
        const vpsAnchorList = vpsInfo.deltaPositionList
        const anchorList = []
        vpsAnchorList.forEach(anchor => {
          const anchorPositionArray = JSON.parse(anchor.position)
          const anchorRotationArray = JSON.parse(anchor.rotation)
          const anchorScaleArray = JSON.parse(anchor.scale)
          //从云测返回的anchor位姿
          const vpsAnchorPose = {
            position: xr.Vector3.createFromNumber(anchorPositionArray[0], anchorPositionArray[1], anchorPositionArray[2]),
            quaternion: xr.Quaternion.createFromNumber(anchorRotationArray[1],anchorRotationArray[2],anchorRotationArray[3],anchorRotationArray[0])
          }
          console.log("--------Anchor的VPSPose是------："+ JSON.stringify(vpsAnchorPose))
          const scale = {x: anchorScaleArray[0], y: anchorScaleArray[1], z: anchorScaleArray[2]}

          let vpsAnchorMatrix = xr.Matrix4.composeTQS(vpsAnchorPose.position, vpsAnchorPose.quaternion, xr.Vector3.ONE)

          let Tmatrix = new xr.Matrix4()
           Tmatrix.setArray(transformMatrix)
           console.log("--------Anchor的转换矩阵是------："+ JSON.stringify(markerTransform.matrixToPose(Tmatrix)))
          let slam_anchor_matrix = Tmatrix.multiply(vpsAnchorMatrix)

          let convertedMatrix = markerTransform.matrixLeftToRightMatrixY(slam_anchor_matrix)

          let convertedPose = markerTransform.matrixToPose(convertedMatrix)

          const convertedEuler = convertedPose.quaternion.toEulerAngles()
          const transformedPose2 = {position: {x: convertedPose.position.x, y: convertedPose.position.y, z: convertedPose.position.z}, rotation: {x: convertedEuler.x, y: convertedEuler.y, z: convertedEuler.z}}
          console.log('transformedPose2: '+JSON.stringify(transformedPose2))
          //const anchorUrl = this.tryGetFirstGlb(anchor.models)
          const anchorData = new AnchorData(anchor.anchorId, 'anchor-' + anchor.anchorId, transformedPose2.position, transformedPose2.rotation, scale, '', anchor.assetId)
          anchorList.push(anchorData)
        });
        this.setData({
          anchorList: anchorList,
          initialVpsTracked: true
        })
        // await this.handleOrderRequest('entrance')
        // await this.handleOrderRequest('introduce')
        //this.refreshAnchorList()
    } catch(err) {
      console.log(err+', retry saveCurrentFrame')
      wx.hideLoading()
      wx.showToast({
        title: '定位失败',
        icon: 'error'
      })
      lastVpsRoamingSuccess = false
      // 确保仅设置一个重试计时器
      // if (!vpsRetryTimeoutId) {
      //   vpsRetryTimeoutId = setTimeout(async () => {
      //     vpsRetryTimeoutId = null;  // 重置定时器标志
      //     await this.saveCurrentFrame(queryPose)
      //   }, 5000)
      // }
    }
  },
  tryGetFirstGlb(modelUrls) {
    if (!modelUrls || !Array.isArray(modelUrls) || modelUrls.length === 0) {
      console.log('modelUrls是空或未定义')
      return null;
    }
    const firstGlb = modelUrls.find(url => url.toLowerCase().endsWith('.glb') || url.toLowerCase().endsWith('.gltf'));
    return firstGlb;
  },
  checkDistance(cameraPos, cameraQuat) {
    const anchorList = this.data.anchorList
    console.log('anchorList length: '+anchorList.length)
    anchorList.forEach((marker, idx) => {
      if (!marker.position) {
        console.log('marker position is undefined')
        return
      }
      const distance = this.calculateDistance(marker.position, cameraPos);
      console.log('cameraPos: '+JSON.stringify(cameraPos)+', markerPos: '+JSON.stringify(marker.position)+'距离'+marker.id+': '+distance)

      if (trackingMarker && trackingMarker.id === marker.id && distance >= distanceThreshold) {
        this.stopTracking()
        try {
          wx.showModal({
            title: '您已离开'+marker.name+'展区',
            content: '是否确认结束体验？',
            confirmText: '确认离开', // 自定义确认按钮的文字
            cancelText: '暂不离开', // 自定义取消按钮的文字
            success: (res) => {
              if (res.confirm) {
                this.handleItemExit(marker)
                this.setData({
                  [`anchorList[${idx}].isActive`]: false
                }).then(() => {
                  this.refreshAnchorList()
                  this.refreshAnchorList()
                  trackingMarker = null
                  this.resumeTracking()
                })
                return
              } else if (res.cancel) {
                this.resumeTracking()
              }
            }
          })
        } catch(err) {
          console.log('on exit current item: '+err)
        }
      }

      else if (!trackingMarker) {
        // 使用新的综合判断方法（需要确保userPose存在）
        console.log('check facing marker')
        let isReady = false;
        if (!cameraQuat) {
          console.log('cameraQuat is null')
          // 如果没有相机朝向信息，回退到仅使用距离判断
          isReady = distance < distanceThreshold;
        } else {
          const isFacingMarker = this.isReadyToEnterMarker(marker.position, cameraPos, cameraQuat, distance);
          console.log(`isFacingMarker ${marker.id}: `+isFacingMarker)
          isReady = distance < distanceThreshold && isFacingMarker
        }

        if (isReady) {
          if (marker.id != this.currentRoom.exitAnchorId) {
            this.stopTracking()
            wx.showModal({
              title: '您已进入'+marker.name+'展区',
              content: '是否确认开启体验？',
              confirmText: '去扫描', // 自定义确认按钮的文字
              cancelText: '稍后再说', // 自定义取消按钮的文字
              success: (res) => {
                if (res.confirm) {
                  this.handleItemEnter(marker)
                  this.setData({
                    [`anchorList[${idx}].isActive`]: true
                  }).then
                  this.refreshAnchorList()
                  trackingMarker = marker
                  this.resumeTracking()
                  return
                } else if (res.cancel) {
                  this.resumeTracking()
                }
              }
            })
            // wx.showToast({
            //   title: '距离'+marker.id+': '+distance,
            // })
          }
          else if (this.currentRoom.nextRoomId) {
            console.log('识别到在下一展厅门口')
            this.stopTracking()
            this.targetRoom = roomsData.find(room => room.id === this.currentRoom.nextRoomId || room.id === this.currentRoom.previousRoomId)
            this.handleRoomTransition(this.targetRoom)
          }
           else {
              console.log('识别到在出口范围内')
              this.stopTracking()
              //this.triggerEvent('leaveIntent')
              this.handleLeaveIntent()
          }
        }
      }
    });
  },
  spawnCameraMesh() {
    if (!this.markerLockComponent) {
      this.markerLockComponent = this.selectComponent('#main-frame')
    }
    if (this.markerLockComponent) {
      this.markerLockComponent.spawnCameraPoseMesh()
    }
  },
  refreshAnchorList() {
    if (!this.markerLockComponent) {
      this.markerLockComponent = this.selectComponent('#main-frame')
    }
    if (this.markerLockComponent) {
      // this.data.anchorList.forEach(anchor => {
      //   console.log('anchor' + anchor.id + ' position: '+JSON.stringify(anchor.position)+', rotation: x: '+(anchor.rotation.x)+', y: '+(anchor.rotation.y)+', z: '+(anchor.rotation.z))
      //   this.markerLockComponent.spawnAnchorItem(anchor)
      // })
      this.markerLockComponent.refreshActiveAnchorList(this.data.anchorList)
    } else {
      console.log('Cannot find markerLock component in the page')
    }
  },
  // 计算三维空间距离的方法
  calculateDistance(pos1, pos2) {
    const dx = pos1.x - pos2.x;
    const dz = pos1.z - pos2.z;
    return Math.sqrt(dx * dx + dz * dz);
  },

  // 判断相机是否准备进入marker（综合考虑距离和朝向）
  // 参数说明：
  // markerPos: marker的位置 {x, y, z}
  // cameraPos: 相机的位置 {x, y, z}
  // cameraQuat: 相机的四元数旋转
  // 返回值: boolean - true表示可以进入marker
  isReadyToEnterMarker(markerPos, cameraPos, cameraQuat, distance) {
    // 1. 计算相机到marker的方向（xz平面）
    const toMarkerX = markerPos.x - cameraPos.x;
    const toMarkerZ = markerPos.z - cameraPos.z;
    const toMarkerLength = Math.sqrt(toMarkerX * toMarkerX + toMarkerZ * toMarkerZ);

    if (toMarkerLength < 0.1) {
      return true; // 距离太近直接进入
    }

    const toMarkerDirX = toMarkerX / toMarkerLength;
    const toMarkerDirZ = toMarkerZ / toMarkerLength;

    // 2. 获取相机前向向量（xz平面）
    const forward = this.quatToForwardXZ(cameraQuat);

    // 3. 点积判断夹角
    const dotProduct = toMarkerDirX * forward.x + toMarkerDirZ * forward.z;
    const angleThreshold = Math.cos(Math.PI / 4); // 45°

    // 调试信息
    const actualAngle = Math.acos(Math.max(-1, Math.min(1, dotProduct))) * 180 / Math.PI;
    console.log(`相机朝向检测 - 到marker角度: ${actualAngle.toFixed(1)}°, 阈值: 45°, 距离: ${distance.toFixed(2)}m`);

    return dotProduct > angleThreshold;
  },
  // 将四元数转为XZ平面上的前向向量
  quatToForwardXZ(q) {
    const x = q.x, y = q.y, z = q.z, w = q.w;

    // forward = q * (0,0,-1) * q^-1
    const forwardX = 2 * (x * z + w * y);
    const forwardY = 2 * (y * z - w * x);
    const forwardZ = 1 - 2 * (x * x + y * y);

    // 投影到XZ平面并归一化
    const length = Math.sqrt(forwardX * forwardX + forwardZ * forwardZ);
    return {
      x: forwardX / length,
      z: forwardZ / length
    };
  },
  showLeaveModal() {
    const unfinishedItemsCount = this.data.unfinishedItemsCount
    wx.showModal({
      title: unfinishedItemsCount > 0 ? `您还有${unfinishedItemsCount}个展项没有看呢！` : '您已到达出口附近，已参观完全部展项。',
      content: unfinishedItemsCount > 0 ? '真的要走吗？' : '是否结束参观？',
      confirmText: unfinishedItemsCount > 0 ? '再看看' : '结束参观', // 自定义确认按钮的文字
      cancelText: unfinishedItemsCount > 0 ? '结束参观' : '再看看', // 自定义取消按钮的文字
      success: (res) => {
        if (res.confirm) {
          console.log('用户点击确认')
          if (unfinishedItemsCount <= 0) {
            this.triggerEvent('endSession')
          } else {
            this.resumeTracking()
          }
          return
        } else if (res.cancel) {
          console.log('用户点击取消')
          if (unfinishedItemsCount > 0) {
            this.triggerEvent('endSession')
          } else {
            this.resumeTracking()
          }
        }
      }
    })
  },
  showTransitionModal(targetRoom) {
    wx.showModal({
      title: `您已到达${targetRoom.id}号展区门口，是否开始参观？`,
      content: '如确定开始参观，请在确认后持稳手机，系统将重新定位',
      confirmText: '确认参观', // 自定义确认按钮的文字
      cancelText: '再等等', // 自定义取消按钮的文字
      success: async (res) => {
        if (res.confirm) {
          //console.log('用户点击确认')
          // vps重新定位
          await this.requestVps(targetRoom.projectId)
          this.currentRoom = targetRoom
        } else if (res.cancel) {
          //console.log('用户点击取消')
          this.resumeTracking()
        }
      }
    })
  },
  stopTracking() {
    if (!this.data.isProximityCheckOn) {
      console.log('stopTracking: not tracking, returning')
      return
    }
    clearInterval(this.data.checkDistanceIntervalId)
    this.setData({
      isProximityCheckOn: false,
      checkDistanceIntervalId: null
    })
  },
  resumeTracking() {
    if (this.data.isProximityCheckOn || !this.data.initialVpsTracked) return
    const checkDistanceIntervalId = setInterval(() => this.checkDistance(this.userPose.position, this.userPose.quaternion), 3000)
    this.setData({
      checkDistanceIntervalId: checkDistanceIntervalId,
      isProximityCheckOn: true
    })
  },
  // 初始AR初始化成功
  handleArInit(evt) {
    this.setData({
      arTracked: true
    })
    wx.hideLoading()
  },
  handleArLost(evt) {
    this.setData({
      arTracked: false
    })
    wx.showLoading({
      title: 'AR初始化',
    })
  },
  // SLAM丢失后，AR再次初始化成功
  handleArTracked(evt) {
    this.setData({
      arTracked: true
    })
    wx.hideLoading()
  },
  // 用于记录原始相机位姿
  handleOriginalCameraPoseTick(evt) {
    const {cameraPos, cameraQuat} = evt.detail
    const cameraEuler = cameraQuat.toEulerAngles()
    this.setData({
      originalPoseText: "originalSlamPose:x:"+cameraPos.x.toFixed(3)+", y:"+cameraPos.y.toFixed(3)+', z:'+cameraPos.z.toFixed(3)+', 旋转:x:'+markerTransform.radianToAngle(cameraEuler.x).toFixed(3)+', y:'+markerTransform.radianToAngle(cameraEuler.y).toFixed(3)+', z:'+markerTransform.radianToAngle(cameraEuler.z).toFixed(3),
    })
    //原始相机位姿
    this.origUserPose = {
      position: {x: cameraPos.x, y: cameraPos.y, z: cameraPos.z},
      quaternion: cameraQuat
    }
  },
  // 用于记录VPS纠偏后的相机位姿
  handleCameraPoseTick(evt) {
    const {cameraPos, cameraQuat, arRawData} = evt.detail
    const cameraEuler = cameraQuat.toEulerAngles()
    this.setData({
      cameraPoseText: "vpsCorrectedPose:x:"+cameraPos.x.toFixed(3)+", y:"+cameraPos.y.toFixed(3)+', z:'+cameraPos.z.toFixed(3)+', 旋转:x:'+markerTransform.radianToAngle(cameraEuler.x).toFixed(3)+', y:'+markerTransform.radianToAngle(cameraEuler.y).toFixed(3)+', z:'+markerTransform.radianToAngle(cameraEuler.z).toFixed(3),
    })
    if (!this.arRawData) {
      this.arRawData = arRawData
    }
    this.userPose = {
      position: {x: cameraPos.x, y: cameraPos.y, z: cameraPos.z},
      quaternion: cameraQuat
    }

    // 用于实时检测anchor与摄像机之间的距离
    if (!this.data.isProximityCheckOn) {
      this.resumeTracking()
    }
  },
  handleItemEnter(markerInfo) {
    this.setData({
      isVisitingItem: true,
      markerWidth: Math.floor(this.data.width * 0.5),
      markerHeight: Math.floor(this.data.width * 0.5)
    })
    // send order request for entering item
    this.handleOrderRequest('start', markerInfo.id, markerInfo.name)
  },
  handleItemExit(markerInfo) {
    this.setData({
      isVisitingItem: false
    })
    // send order request for exiting item
    this.handleOrderRequest('stop', markerInfo.id, markerInfo.name)
  },
  handleEndSession(evt) {
    this.handleOrderRequest('exit')
  },
  async handleLeaveIntent(evt = undefined) {
    await this.getQueryItems()
    if (this.markerLockComponent) {
      markerLockComponent.showLeaveModal()
    } else {
      console.log('Cannot find markerLock component in the page')
    }
  },
  async handleRoomTransition(targetRoom) {
    console.log('targetRoom: '+JSON.stringify(targetRoom))
    if (this.markerLockComponent) {
      markerLockComponent.showTransitionModal(targetRoom)
    } else {
      console.log('Cannot find markerLock component in the page')
    }
  },
  async handleTrackerPositionReceived(evt) {
    const item = evt.detail
    const markerPose = {
      id: item.id,
      position: item.position,
      rotation: item.rotation
    }
    this.calculateTransformationMatrices(markerPose)
    const anchorList = this.data.anchorList
    anchorList.forEach(marker => {
      if (marker.id === 'entryMarker') {
        marker.pos = markerPose.position
      }
      else {
        const translationTransform = this.data.translationTransform
        const rotationTransform = this.data.rotationTransform
        const markerPos1 = wx.getStorageSync(marker.id)
        if (markerPos1) {
          marker.pos = markerTransform.getTransformedPosition(translationTransform, rotationTransform, markerPos1.position)
        }
      }
    });
    this.setData({
      anchorList: anchorList,
      initialVpsTracked: true
    })
    await this.handleOrderRequest('entrance')
    await this.handleOrderRequest('introduce')
  },
  async handleOrderRequest(instruction, project_id, project_name) {
    const agentChatViewerComponent = this.selectComponent('#agent-chat-viewer')
    if (agentChatViewerComponent) {
      await agentChatViewerComponent.sendOrderMessage(instruction, project_id, project_name)
    } else {
      console.log('Cannot find agentChatViewerComponent.')
    }
  },
  async getQueryItems() {
    const agentChatViewerComponent = this.selectComponent('#agent-chat-viewer')
    if (agentChatViewerComponent) {
      const queryItems = await agentChatViewerComponent.getQueryItems()
      const unfinishedItemsCount = queryItems.filter(item => item.state === 0).length
      console.log('还有'+unfinishedItemsCount+'项没有看完。')
      this.setData({
        unfinishedItemsCount: unfinishedItemsCount
      })
    } else {
      console.log('Cannot find agentChatViewerComponent.')
    }
  },
});