# 展项进入/离开判断机制改进测试

## 改进内容

### 1. 离开展项判断机制优化
- **原逻辑**: 仅基于距离判断（距离 >= 1.8m 即触发离开）
- **新逻辑**: 先判断朝向，再判断距离
  - 用户必须背对展项（角度 > 90°）且距离超过阈值才触发离开
  - 更符合用户交互习惯，避免用户在展项附近移动时误触发

### 2. 防频繁弹窗机制
- **弹窗冷却**: 同一展项10秒内不重复弹窗
- **用户拒绝冷却**: 用户拒绝后30秒内不再弹窗
- **自动清理**: 定期清理过期记录，避免内存泄漏

## 测试场景

### 场景1: 离开展项判断
1. 用户进入展项范围并确认体验
2. 用户在展项附近移动（距离可能超过1.8m但仍面向展项）
   - **预期**: 不触发离开弹窗
3. 用户背对展项并走远（距离超过1.8m且背对展项）
   - **预期**: 触发离开弹窗

### 场景2: 防频繁弹窗
1. 用户接近展项，触发进入弹窗
2. 用户点击"稍后再说"拒绝进入
3. 用户在30秒内再次接近同一展项
   - **预期**: 不再弹窗
4. 30秒后用户再次接近
   - **预期**: 重新弹窗

### 场景3: 弹窗冷却
1. 用户接近展项，触发弹窗
2. 用户离开范围，弹窗消失
3. 用户在10秒内再次接近
   - **预期**: 不弹窗
4. 10秒后再次接近
   - **预期**: 重新弹窗

## 关键参数

```javascript
const MODAL_COOLDOWN = 10000      // 弹窗冷却时间10秒
const REJECTION_COOLDOWN = 30000  // 用户拒绝后冷却时间30秒
const distanceThreshold = 1.8     // 距离阈值1.8米
const angleThreshold = 90         // 离开角度阈值90度
```

## 调试信息

系统会在控制台输出以下调试信息：
- 相机朝向检测角度和距离
- 离开检测的角度、距离和背对状态
- 弹窗冷却期跳过信息
- 用户拒绝冷却期跳过信息

## 注意事项

1. 确保相机朝向数据（cameraQuat）正常获取
2. 如果朝向数据异常，系统会回退到仅使用距离判断
3. 用户主动进入展项时会清除该展项的拒绝记录
4. 页面卸载时会清理所有防频繁弹窗记录
