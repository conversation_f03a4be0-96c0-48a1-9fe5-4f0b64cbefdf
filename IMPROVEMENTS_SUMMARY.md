# 展项进入/离开判断机制改进总结

## 问题分析

### 原有问题
1. **离开展项判断不够智能**: 仅基于距离判断，用户在展项附近移动时容易误触发离开弹窗
2. **频繁弹窗干扰用户体验**: 
   - 用户未响应前会持续触发弹窗
   - 用户拒绝后仍会频繁出现弹窗

## 解决方案

### 1. 改进离开展项判断机制

**核心改进**: 先根据朝向再根据距离进行判断

```javascript
// 新增方法: isLeavingMarker
isLeavingMarker(markerPos, cameraPos, cameraQuat, distance) {
  // 1. 距离必须超过阈值
  if (distance < distanceThreshold) return false;
  
  // 2. 计算用户朝向与展项的角度关系
  // 3. 只有当用户背对展项(角度>90°)且距离超过阈值时才判定为离开
  const isBackToMarker = actualAngle > 90;
  return isBackToMarker;
}
```

**优势**:
- 更符合用户交互习惯
- 避免用户在展项附近正常移动时误触发
- 只有明确背离展项时才提示离开

### 2. 实现防频繁弹窗机制

**核心机制**: 多层次的弹窗控制

```javascript
// 防频繁弹窗的全局变量
let lastModalTime = {}        // 记录每个marker上次弹窗时间
let userRejectedMarkers = {}  // 记录用户拒绝的marker和时间
const MODAL_COOLDOWN = 10000      // 弹窗冷却时间10秒
const REJECTION_COOLDOWN = 30000  // 用户拒绝后冷却时间30秒
```

**控制逻辑**:
1. **弹窗冷却**: 同一展项10秒内不重复弹窗
2. **用户拒绝冷却**: 用户拒绝后30秒内不再弹窗  
3. **智能重置**: 用户主动进入展项时清除拒绝记录
4. **自动清理**: 定期清理过期记录避免内存泄漏

### 3. 代码实现细节

#### 修改的核心方法

1. **checkDistance()**: 
   - 离开判断改用 `isLeavingMarker()` 方法
   - 进入判断增加 `shouldShowEnterModal()` 检查

2. **新增方法**:
   - `isLeavingMarker()`: 综合朝向和距离的离开判断
   - `shouldShowEnterModal()`: 防频繁弹窗检查
   - `cleanupExpiredRecords()`: 清理过期记录

3. **生命周期管理**:
   - `onUnload()`: 清理所有防频繁弹窗记录
   - `resumeTracking()`: 定期清理过期记录

## 技术特点

### 1. 智能朝向判断
- 使用四元数计算相机前向向量
- 计算相机朝向与展项方向的夹角
- 支持朝向数据异常时的降级处理

### 2. 内存管理
- 自动清理过期的弹窗记录
- 页面卸载时完全清理
- 避免长时间运行导致的内存泄漏

### 3. 用户体验优化
- 减少不必要的弹窗干扰
- 保持交互的响应性
- 提供清晰的调试信息

## 测试建议

1. **离开判断测试**:
   - 在展项附近移动，验证不会误触发
   - 背对展项走远，验证正确触发

2. **防频繁弹窗测试**:
   - 拒绝进入后短时间内再次接近
   - 验证冷却期机制是否生效

3. **边界情况测试**:
   - 朝向数据异常时的降级处理
   - 长时间使用后的内存表现

## 配置参数

可根据实际需求调整以下参数:

```javascript
const MODAL_COOLDOWN = 10000      // 弹窗冷却时间
const REJECTION_COOLDOWN = 30000  // 拒绝后冷却时间  
const distanceThreshold = 1.8     // 距离阈值
const angleThreshold = 90         // 离开角度阈值
```

## 总结

此次改进显著提升了用户体验:
1. **更智能的离开判断**: 基于朝向+距离的综合判断
2. **更友好的交互**: 有效防止频繁弹窗干扰
3. **更稳定的性能**: 完善的内存管理和错误处理

改进后的系统能够更准确地理解用户意图，减少误操作，提供更流畅的AR体验。
